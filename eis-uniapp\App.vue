<!--
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
-->

<script setup>
/**
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
 */

import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

// 应用启动时
onLaunch(() => {
	console.log('EisShop App Launch')
	// 初始化应用配置
	initApp()
})

// 应用显示时
onShow(() => {
	console.log('EisShop App Show')
})

// 应用隐藏时
onHide(() => {
	console.log('EisShop App Hide')
})

// 初始化应用
const initApp = () => {
	// 设置状态栏样式
	uni.setNavigationBarColor({
		frontColor: '#000000',
		backgroundColor: '#ffffff'
	})

	// 检查更新
	checkUpdate()
}

// 检查应用更新
const checkUpdate = () => {
	// #ifdef APP-PLUS
	const updateManager = uni.getUpdateManager()

	updateManager.onCheckForUpdate((res) => {
		console.log('检查更新结果:', res.hasUpdate)
	})

	updateManager.onUpdateReady(() => {
		uni.showModal({
			title: '更新提示',
			content: '新版本已经准备好，是否重启应用？',
			success: (res) => {
				if (res.confirm) {
					updateManager.applyUpdate()
				}
			}
		})
	})

	updateManager.onUpdateFailed(() => {
		uni.showToast({
			title: '更新失败',
			icon: 'none'
		})
	})
	// #endif
}
</script>

<style>
/**
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
 */

/* 全局样式 */
page {
	background-color: #f8f8f8;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 主题色彩变量 */
:root {
	--primary-color: #3cc51f;
	--secondary-color: #f6f6f6;
	--success-color: #07c160;
	--warning-color: #ff976a;
	--error-color: #ed4014;
	--text-color: #333333;
	--text-color-light: #666666;
	--text-color-lighter: #999999;
	--border-color: #e5e5e5;
	--background-color: #f8f8f8;
}

/* 通用样式 */
.container {
	padding: 20rpx;
}

.text-center {
	text-align: center;
}

.text-left {
	text-align: left;
}

.text-right {
	text-align: right;
}

.flex {
	display: flex;
}

.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}

.flex-between {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.flex-column {
	display: flex;
	flex-direction: column;
}

/* 边距样式 */
.m-10 { margin: 10rpx; }
.m-20 { margin: 20rpx; }
.m-30 { margin: 30rpx; }

.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }

.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

/* 字体大小 */
.text-xs { font-size: 20rpx; }
.text-sm { font-size: 24rpx; }
.text-base { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }
.text-2xl { font-size: 40rpx; }

/* 字体颜色 */
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-gray { color: var(--text-color-light); }
.text-light { color: var(--text-color-lighter); }

/* 背景颜色 */
.bg-white { background-color: #ffffff; }
.bg-gray { background-color: var(--background-color); }
.bg-primary { background-color: var(--primary-color); }

/* 圆角 */
.rounded { border-radius: 8rpx; }
.rounded-lg { border-radius: 16rpx; }
.rounded-xl { border-radius: 24rpx; }
.rounded-full { border-radius: 50%; }

/* 阴影 */
.shadow {
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}
</style>
