/*
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
*/

// EisShop移动端配置文件

// 环境配置
const ENV = {
  // 开发环境
  development: {
    baseURL: 'http://*********/api/v1',
    imageURL: 'http://*********',
    timeout: 15000,
    debug: true
  },
  // 生产环境
  production: {
    baseURL: 'https://api.shop.bwzj.top/api/v1',
    imageURL: 'https://cdn.shop.bwzj.top',
    timeout: 10000,
    debug: false
  }
}

// 当前环境（根据实际情况修改）
const currentEnv = 'development'

// 应用配置
export const config = {
  // 应用信息
  app: {
    name: 'EisShop',
    version: '1.0.0',
    description: 'EisShop便捷式电商系统移动端'
  },
  
  // 网络配置
  network: {
    ...ENV[currentEnv]
  },
  
  // 存储配置
  storage: {
    tokenKey: 'eisshop_token',
    userKey: 'eisshop_user',
    cartKey: 'eisshop_cart',
    historyKey: 'eisshop_history'
  },
  
  // 页面配置
  pages: {
    // 默认页面
    home: '/pages/home/<USER>',
    login: '/pages/login/login',
    
    // Tab页面
    tabPages: [
      '/pages/home/<USER>',
      '/pages/classify/classify',
      '/pages/discover/discover',
      '/pages/cart/cart',
      '/pages/my/my'
    ]
  },
  
  // 主题配置
  theme: {
    primaryColor: '#0BAB59',
    secondaryColor: '#f6f6f6',
    successColor: '#07c160',
    warningColor: '#ff976a',
    errorColor: '#ed4014'
  },
  
  // 功能开关
  features: {
    // 是否启用登录
    enableLogin: true,
    // 是否启用购物车
    enableCart: true,
    // 是否启用积分
    enablePoints: true,
    // 是否启用优惠券
    enableCoupon: true,
    // 是否启用分享
    enableShare: true
  },
  
  // 第三方配置
  thirdParty: {
    // 微信配置
    wechat: {
      appId: '',
      appSecret: ''
    },
    // 支付宝配置
    alipay: {
      appId: ''
    }
  }
}

// 获取配置
export const getConfig = (key) => {
  if (!key) return config
  
  const keys = key.split('.')
  let result = config
  
  for (const k of keys) {
    if (result && typeof result === 'object' && k in result) {
      result = result[k]
    } else {
      return undefined
    }
  }
  
  return result
}

// 设置配置
export const setConfig = (key, value) => {
  const keys = key.split('.')
  let target = config
  
  for (let i = 0; i < keys.length - 1; i++) {
    const k = keys[i]
    if (!target[k] || typeof target[k] !== 'object') {
      target[k] = {}
    }
    target = target[k]
  }
  
  target[keys[keys.length - 1]] = value
}

export default config
