{"id": "eisshop-uniapp", "name": "EisShop移动端商城", "displayName": "EisShop便捷式电商系统移动端", "version": "1.0.0", "description": "EisShop便捷式电商系统移动端，基于Vue3+UniApp开发，支持H5、微信小程序、APP等多端运行", "keywords": ["EisShop", "电商系统", "Vue3", "UniApp", "多端商城"], "author": {"name": "EisShop Team", "email": "<EMAIL>", "url": "https://shop.bwzj.top"}, "license": "Commercial", "homepage": "https://shop.bwzj.top", "repository": {"type": "git", "url": "https://github.com/eisshop/eisshop-uniapp.git"}, "engines": {"HBuilderX": "^3.0.0", "node": ">=16.0.0"}, "dependencies": {"pinia": "^2.1.7", "vue": "^3.3.4", "@dcloudio/uni-app": "3.0.0-3081220231117001", "@dcloudio/uni-components": "3.0.0-3081220231117001", "@dcloudio/uni-h5": "3.0.0-3081220231117001", "@dcloudio/uni-mp-alipay": "3.0.0-3081220231117001", "@dcloudio/uni-mp-baidu": "3.0.0-3081220231117001", "@dcloudio/uni-mp-qq": "3.0.0-3081220231117001", "@dcloudio/uni-mp-toutiao": "3.0.0-3081220231117001", "@dcloudio/uni-mp-weixin": "3.0.0-3081220231117001", "@dcloudio/uni-quickapp-webview": "3.0.0-3081220231117001"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/vite-plugin-uni": "3.0.0-3081220231117001", "vite": "^4.4.11", "sass": "^1.69.5", "sass-loader": "^13.3.2"}, "scripts": {"dev:h5": "vite", "build:h5": "vite build", "dev:mp-weixin": "uni -p mp-weixin", "build:mp-weixin": "uni build -p mp-weixin", "dev:app": "uni -p app", "build:app": "uni build -p app", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "uni-app": {"scripts": {}}, "dcloudext": {"category": ["uni-app前端项目", "电商系统"]}}