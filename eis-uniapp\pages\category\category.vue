<!--
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
-->

<template>
	<view class="category-container">
		<view class="category-content">
			<text class="category-title">商品分类</text>
			<text class="category-desc">敬请期待...</text>
		</view>
	</view>
</template>

<script setup>
/**
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
 */

import { onMounted } from 'vue'

// 页面加载时执行
onMounted(() => {
	console.log('分类页面加载完成')
})
</script>

<style scoped>
.category-container {
	min-height: 100vh;
	background-color: #f8f8f8;
	display: flex;
	align-items: center;
	justify-content: center;
}

.category-content {
	text-align: center;
}

.category-title {
	font-size: 48rpx;
	color: #333333;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.category-desc {
	font-size: 28rpx;
	color: #666666;
	display: block;
}
</style>
