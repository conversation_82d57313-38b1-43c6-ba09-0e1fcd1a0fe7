<!--
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
-->

<template>
	<view class="home-container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input" @click="goToSearch">
				<text class="search-placeholder">搜索商品</text>
			</view>
		</view>

		<!-- 轮播图 -->
		<view class="banner-section">
			<swiper class="banner-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
				<swiper-item v-for="(banner, index) in bannerList" :key="index">
					<image class="banner-image" :src="banner.image" mode="aspectFill" @click="onBannerClick(banner)"></image>
				</swiper-item>
			</swiper>
		</view>

		<!-- 快捷导航 -->
		<view class="nav-section">
			<view class="nav-grid">
				<view class="nav-item" v-for="(nav, index) in navList" :key="index" @click="onNavClick(nav)">
					<view class="nav-icon">
						<text class="nav-icon-text">{{ nav.icon }}</text>
					</view>
					<text class="nav-title">{{ nav.title }}</text>
				</view>
			</view>
		</view>

		<!-- 推荐商品 -->
		<view class="goods-section">
			<view class="section-header">
				<text class="section-title">推荐商品</text>
				<text class="section-more" @click="goToGoodsList">更多</text>
			</view>
			<view class="goods-grid">
				<view class="goods-item" v-for="(goods, index) in goodsList" :key="index" @click="goToGoodsDetail(goods)">
					<image class="goods-image" :src="goods.image" mode="aspectFill"></image>
					<view class="goods-info">
						<text class="goods-name">{{ goods.name }}</text>
						<view class="goods-price-row">
							<text class="goods-price">¥{{ goods.price }}</text>
							<text class="goods-original-price" v-if="goods.original_price">¥{{ goods.original_price }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
/**
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
 */

import { ref, onMounted } from 'vue'

// 响应式数据
const bannerList = ref([
	{
		id: 1,
		image: '/static/banner/banner1.jpg',
		title: '新品上市',
		url: '/pages/goods/list'
	},
	{
		id: 2,
		image: '/static/banner/banner2.jpg',
		title: '限时特惠',
		url: '/pages/activity/detail'
	},
	{
		id: 3,
		image: '/static/banner/banner3.jpg',
		title: '品牌专区',
		url: '/pages/brand/list'
	}
])

const navList = ref([
	{ id: 1, icon: '🛍️', title: '全部分类', url: '/pages/category/category' },
	{ id: 2, icon: '🔥', title: '限时抢购', url: '/pages/activity/seckill' },
	{ id: 3, icon: '🎁', title: '优惠券', url: '/pages/coupon/list' },
	{ id: 4, icon: '⭐', title: '品牌馆', url: '/pages/brand/list' },
	{ id: 5, icon: '📱', title: '数码专区', url: '/pages/goods/list?category=digital' },
	{ id: 6, icon: '👗', title: '服装专区', url: '/pages/goods/list?category=clothing' },
	{ id: 7, icon: '🏠', title: '居家生活', url: '/pages/goods/list?category=home' },
	{ id: 8, icon: '🍎', title: '生鲜食品', url: '/pages/goods/list?category=food' }
])

const goodsList = ref([
	{
		id: 1,
		name: 'iPhone 15 Pro Max',
		image: '/static/goods/phone1.jpg',
		price: '9999.00',
		original_price: '10999.00'
	},
	{
		id: 2,
		name: '华为Mate60 Pro',
		image: '/static/goods/phone2.jpg',
		price: '6999.00',
		original_price: '7999.00'
	},
	{
		id: 3,
		name: '小米14 Ultra',
		image: '/static/goods/phone3.jpg',
		price: '5999.00',
		original_price: '6999.00'
	},
	{
		id: 4,
		name: 'MacBook Pro',
		image: '/static/goods/laptop1.jpg',
		price: '12999.00',
		original_price: '14999.00'
	}
])

// 页面生命周期
onMounted(() => {
	console.log('首页加载完成')
	loadData()
})

// 方法
const loadData = () => {
	// TODO: 调用API获取轮播图、导航、商品数据
	console.log('加载首页数据')
}

const goToSearch = () => {
	uni.navigateTo({
		url: '/pages/search/search'
	})
}

const onBannerClick = (banner) => {
	console.log('点击轮播图:', banner)
	if (banner.url) {
		uni.navigateTo({
			url: banner.url
		})
	}
}

const onNavClick = (nav) => {
	console.log('点击导航:', nav)
	if (nav.url) {
		uni.navigateTo({
			url: nav.url
		})
	}
}

const goToGoodsList = () => {
	uni.navigateTo({
		url: '/pages/goods/list'
	})
}

const goToGoodsDetail = (goods) => {
	console.log('点击商品:', goods)
	uni.navigateTo({
		url: `/pages/goods/detail?id=${goods.id}`
	})
}
</script>

<style scoped>
.home-container {
	background-color: #f8f8f8;
	min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
	background-color: #ffffff;
	padding: 20rpx 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-input {
	background-color: #f5f5f5;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
}

.search-placeholder {
	color: #999999;
	font-size: 28rpx;
}

/* 轮播图 */
.banner-section {
	margin: 20rpx 30rpx;
}

.banner-swiper {
	height: 300rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.banner-image {
	width: 100%;
	height: 100%;
}

/* 快捷导航 */
.nav-section {
	background-color: #ffffff;
	margin: 20rpx 30rpx;
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
}

.nav-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx;
}

.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.nav-icon {
	width: 80rpx;
	height: 80rpx;
	background-color: #f0f9ff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 10rpx;
}

.nav-icon-text {
	font-size: 36rpx;
}

.nav-title {
	font-size: 24rpx;
	color: #333333;
}

/* 商品区域 */
.goods-section {
	background-color: #ffffff;
	margin: 20rpx 30rpx;
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.section-more {
	font-size: 26rpx;
	color: #3cc51f;
}

.goods-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.goods-item {
	background-color: #ffffff;
	border-radius: 15rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.goods-image {
	width: 100%;
	height: 200rpx;
}

.goods-info {
	padding: 20rpx;
}

.goods-name {
	font-size: 26rpx;
	color: #333333;
	display: block;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.goods-price-row {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.goods-price {
	font-size: 30rpx;
	color: #ff4757;
	font-weight: bold;
}

.goods-original-price {
	font-size: 24rpx;
	color: #999999;
	text-decoration: line-through;
}
</style>
