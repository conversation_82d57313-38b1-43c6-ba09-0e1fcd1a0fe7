<!--
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
-->

<template>
	<view class="user-container">
		<view class="user-content">
			<text class="user-title">个人中心</text>
			<text class="user-desc">用户功能开发中...</text>
		</view>
	</view>
</template>

<script setup>
/**
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
 */

import { onMounted } from 'vue'

// 页面加载时执行
onMounted(() => {
	console.log('个人中心页面加载完成')
})
</script>

<style scoped>
.user-container {
	min-height: 100vh;
	background-color: #f8f8f8;
	display: flex;
	align-items: center;
	justify-content: center;
}

.user-content {
	text-align: center;
}

.user-title {
	font-size: 48rpx;
	color: #333333;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.user-desc {
	font-size: 28rpx;
	color: #666666;
	display: block;
}
</style>
