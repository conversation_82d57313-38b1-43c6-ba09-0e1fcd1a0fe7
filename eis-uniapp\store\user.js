/*
 +----------------------------------------------------------------------
 | EisShop [ EisShop便捷式电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed EisShop并不是自由软件，未经许可不能去掉EisShop相关版权
 +----------------------------------------------------------------------
 | Author: EisShop Team <<EMAIL>>
 +----------------------------------------------------------------------
*/

import { defineStore } from 'pinia'
import { config } from '@/config/index.js'
import {
  login,
  loginByPhone,
  loginByWechat,
  register,
  logout,
  getUserInfo,
  updateUserInfo,
  changePassword,
  bindPhone,
  bindEmail,
  refreshToken
} from '@/api/auth.js'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 用户信息
    userInfo: null,
    // 登录token
    token: '',
    // 刷新token
    refreshToken: '',
    // 是否已登录
    isLoggedIn: false,
    // 用户权限
    permissions: [],
    // 登录方式
    loginType: '', // password|phone|wechat
    // 用户设置
    settings: {
      // 是否开启消息推送
      enablePush: true,
      // 是否开启声音
      enableSound: true,
      // 是否开启震动
      enableVibrate: true
    }
  }),

  getters: {
    // 用户昵称
    nickname: (state) => {
      return state.userInfo?.nickname || state.userInfo?.username || '用户'
    },
    
    // 用户头像
    avatar: (state) => {
      return state.userInfo?.avatar || '/static/img/default-avatar.png'
    },
    
    // 用户等级
    level: (state) => {
      return state.userInfo?.level || 1
    },
    
    // 用户积分
    points: (state) => {
      return state.userInfo?.points || 0
    },
    
    // 用户余额
    balance: (state) => {
      return state.userInfo?.balance || 0
    },
    
    // 是否为VIP
    isVip: (state) => {
      return state.userInfo?.is_vip || false
    }
  },

  actions: {
    // 密码登录
    async loginAction(loginData) {
      try {
        const response = await login(loginData)

        if (response.code === 0 || response.code === 200) {
          this.token = response.data.token || response.data.access_token
          this.refreshToken = response.data.refresh_token || ''
          this.userInfo = response.data.user || response.data.userInfo
          this.isLoggedIn = true
          this.loginType = 'password'

          // 保存到本地存储
          this._saveUserData()

          return response
        } else {
          throw new Error(response.message || '登录失败')
        }
      } catch (error) {
        console.error('密码登录失败:', error)
        throw error
      }
    },

    // 手机号登录
    async loginByPhoneAction(phoneData) {
      try {
        const response = await loginByPhone(phoneData)

        if (response.code === 0 || response.code === 200) {
          this.token = response.data.token || response.data.access_token
          this.refreshToken = response.data.refresh_token || ''
          this.userInfo = response.data.user || response.data.userInfo
          this.isLoggedIn = true
          this.loginType = 'phone'

          // 保存到本地存储
          this._saveUserData()

          return response
        } else {
          throw new Error(response.message || '登录失败')
        }
      } catch (error) {
        console.error('手机号登录失败:', error)
        throw error
      }
    },

    // 微信登录
    async loginByWechatAction(wechatData) {
      try {
        const response = await loginByWechat(wechatData)

        if (response.code === 0 || response.code === 200) {
          this.token = response.data.token || response.data.access_token
          this.refreshToken = response.data.refresh_token || ''
          this.userInfo = response.data.user || response.data.userInfo
          this.isLoggedIn = true
          this.loginType = 'wechat'

          // 保存到本地存储
          this._saveUserData()

          return response
        } else {
          throw new Error(response.message || '登录失败')
        }
      } catch (error) {
        console.error('微信登录失败:', error)
        throw error
      }
    },

    // 用户注册
    async registerAction(registerData) {
      try {
        const response = await register(registerData)

        if (response.code === 0 || response.code === 200) {
          // 注册成功后可以选择自动登录
          if (response.data.token) {
            this.token = response.data.token || response.data.access_token
            this.refreshToken = response.data.refresh_token || ''
            this.userInfo = response.data.user || response.data.userInfo
            this.isLoggedIn = true
            this.loginType = 'password'

            // 保存到本地存储
            this._saveUserData()
          }

          return response
        } else {
          throw new Error(response.message || '注册失败')
        }
      } catch (error) {
        console.error('注册失败:', error)
        throw error
      }
    },

    // 退出登录
    async logoutAction() {
      try {
        // 调用后端退出接口
        await logout()
      } catch (error) {
        console.error('退出登录接口调用失败:', error)
      } finally {
        // 无论接口是否成功，都清除本地数据
        this._clearUserData()

        // 跳转到登录页
        uni.reLaunch({
          url: config.pages.login || '/pages/login/login'
        })
      }
    },

    // 获取用户信息
    async fetchUserInfo() {
      try {
        const response = await getUserInfo()
        
        if (response.code === 0) {
          this.userInfo = response.data
          uni.setStorageSync(config.storage.userKey, this.userInfo)
          return response.data
        } else {
          throw new Error(response.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果token失效，自动退出登录
        if (error.message.includes('401') || error.message.includes('登录')) {
          this.logout()
        }
        throw error
      }
    },

    // 更新用户信息
    updateUserInfo(userInfo) {
      this.userInfo = { ...this.userInfo, ...userInfo }
      uni.setStorageSync(config.storage.userKey, this.userInfo)
    },

    // 初始化用户状态
    initUserState() {
      const token = uni.getStorageSync(config.storage.tokenKey)
      const userInfo = uni.getStorageSync(config.storage.userKey)
      
      if (token && userInfo) {
        this.token = token
        this.userInfo = userInfo
        this.isLoggedIn = true
        
        // 刷新用户信息
        this.fetchUserInfo().catch(() => {
          // 如果刷新失败，可能token已过期
          this.logout()
        })
      }
    },

    // 检查登录状态
    checkLoginStatus() {
      if (!this.isLoggedIn) {
        uni.showModal({
          title: '提示',
          content: '请先登录',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: config.pages.login
              })
            }
          }
        })
        return false
      }
      return true
    },

    // 更新用户设置
    updateSettings(settings) {
      this.settings = { ...this.settings, ...settings }
    },

    // 增加积分
    addPoints(points) {
      if (this.userInfo) {
        this.userInfo.points = (this.userInfo.points || 0) + points
        uni.setStorageSync(config.storage.userKey, this.userInfo)
      }
    },

    // 减少积分
    reducePoints(points) {
      if (this.userInfo) {
        this.userInfo.points = Math.max((this.userInfo.points || 0) - points, 0)
        uni.setStorageSync(config.storage.userKey, this.userInfo)
      }
    },

    // 增加余额
    addBalance(amount) {
      if (this.userInfo) {
        this.userInfo.balance = (this.userInfo.balance || 0) + amount
        uni.setStorageSync(config.storage.userKey, this.userInfo)
      }
    },

    // 减少余额
    reduceBalance(amount) {
      if (this.userInfo) {
        this.userInfo.balance = Math.max((this.userInfo.balance || 0) - amount, 0)
        uni.setStorageSync(config.storage.userKey, this.userInfo)
      }
    }
  }
})
